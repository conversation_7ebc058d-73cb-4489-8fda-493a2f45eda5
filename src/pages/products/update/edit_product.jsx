import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleCustomError } from "../../../hooks/handleCustomError";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useListAllBrandsQuery } from "../../../feature/api/brandsDataApiSlice";
import FormikField from "../../../components/formikField";
import {
  useCreateUpdateVariationsProductsMutation,
  useDeleteProductDetailMutation,
  useEditProductMutation,
  useEditProductDetailMutation,
  useGetProductdetailListQuery,
  useGetVariationsProductsMutation,
  useSingleProductQuery,
  useCreateProductDetailMutation,
} from "../../../feature/api/productDataApiSlice";
import { useNavigate, useParams } from "react-router-dom";
import { useListAllCategoriesQuery } from "../../../feature/api/categoriesDataApiSlice";
import { useGetAllSubCategoriesQuery } from "../../../feature/api/subCategoriesDataApiSlice";
import { useGetAllChildCategoriesQuery } from "../../../feature/api/childCategoriesDataApiSlice";
import { useEffect, useMemo, useState, useRef } from "react";
import WebLoader from "../../../components/webLoader";
import { useSelector } from "react-redux";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useListAllAttributesQuery } from "../../../feature/api/attributesDataApiSlice";
import { useGetAllAttributesValuesQuery } from "../../../feature/api/attributesValuesDataApiSlice";
import { Table } from "../../../components/datatable";
import { PaginationComponent } from "../../../components/pagination";
import useConfirmDelete from "../../../hooks/useConfirmDelete";

// Define initial values with attribute_id as an array
const initialValues = {
  image: null,
  title: "",
  title_ar: "",
  brand_id: "",
  category_id: "",
  sub_category_id: "",
  child_category_id: "",
  description: "",
  description_ar: "",
  is_publish: "",
  note: "",
  product_slug: "",
  attribute_id: [], // Initialize attribute_id as an empty array
};

// Update validation schema to include attribute_id as an array
const validation = yup.object().shape({
  title: yup.string().required().label("Product Title in English"),
  title_ar: yup.string().required().label("Product Title in Arabic"),
  brand_id: yup.string().required().label("Brand"),
  category_id: yup.string().required().label("Category"),
  sub_category_id: yup.string().label("Sub Category"),
  child_category_id: yup.string().label("Child Category"),
  description: yup.string().label("Description in English"),
  description_ar: yup.string().label("Description in Arabic"),
  is_publish: yup.string().required().label("Status"),
  image: yup.mixed().label("Product Image"),
  note: yup.string().label("Note"),
  attribute_id: yup.array().of(yup.string()).label("Product Variations"), // Validate attribute_id as an array of strings
});

export default function EditProduct() {
  const navigation = useNavigate();
  const { id } = useParams();
  const productId = parseInt(id);
  const activePage = "Products Master";
  const linkHref = "/dashboard";

  const [currentPage, setCurrentPage] = useState(1);
  const [filterStatus, setFilterStatus] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");

  /* ****************  Start Product Detail Filter ****************** */
  const handleStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* ****************  End Product Detail Filter ****************** */

  /* **************** Start Paginatation ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Paginatation ***************** */

  const { data: productDetailListResp, isProductDetailListingLoading } =
    useGetProductdetailListQuery({
      page: currentPage,
      product_id: productId,
      status: parseInt(filterStatus),
      keywords: filterKeywords,
    });
  const productDetailList = useMemo(() => {
    if (!productDetailListResp?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return productDetailListResp?.data?.list;
  }, [currentPage, productDetailListResp?.data.list]);
  const pageData = useMemo(
    () => productDetailListResp?.data?.page ?? null,
    [productDetailListResp]
  );

  // const [selectedBranchId, setSelectedBranchId] = useState("");
  // const [selectedProductId, setSelectedProductId] = useState("");

  /* **************** Start fetching single product data ******************* */
  let { data: singleProduct, isLoading: isProductLoading } =
    useSingleProductQuery({
      id: productId,
    });

  const productData = useMemo(() => {
    return singleProduct?.data || null;
  }, [singleProduct]);

  /* **************** Start Fetch Product Variations ******************* */
  const [getVariationsProducts] = useGetVariationsProductsMutation();
  const [singleProductVariations, setSingleProductVariations] = useState(null);
  const [isProductVariationLoading, setIsProductVariationLoading] =
    useState(false);

  // Fetch product variations when product data is available
  useEffect(() => {
    if (productId) {
      const fetchProductVariations = async () => {
        setIsProductVariationLoading(true);
        try {
          const response = await getVariationsProducts({
            product_id: productId,
          }).unwrap();
          setSingleProductVariations(response);
        } catch (error) {
          console.error("Error fetching product variations:", error);
          setSingleProductVariations(null);
        } finally {
          setIsProductVariationLoading(false);
        }
      };
      fetchProductVariations();
    }
  }, [productId, getVariationsProducts]);

  // Extract attribute_ids from singleProductVariations
  const variationAttributeIds = useMemo(() => {
    if (!singleProductVariations?.data) return [];
    return singleProductVariations.data.map((variation) =>
      variation.attribute_id.toString()
    );
  }, [singleProductVariations]);

  /* **************** End Fetch Product Variations ******************* */

  useEffect(() => {
    if (isProductLoading || isProductVariationLoading) return;

    if (!productData) {
      handleCustomError("Error was found, please try again later!");
      navigation("/products");
    } else {
      setSelectedCategoryId(parseInt(productData?.category_id));
      setSelectedSubCategoryId(parseInt(productData?.sub_category_id));
    }
  }, [isProductLoading, productData, isProductVariationLoading, navigation]);

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector(
    (state) => state.commonState.generalStatus
  );
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list General Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start List Brand List ******************* */
  const brandResp = useListAllBrandsQuery();
  const brand = brandResp.data?.data || [];
  const brandAry = brand.map((brand) => ({
    value: brand.id,
    label: brand.title,
  }));
  /* **************** End List Brand List ******************* */

  /* **************** Start Category List ******************* */
  const categoryListResp = useListAllCategoriesQuery();
  const categoryList = categoryListResp.data?.data || [];
  const categoriesList = categoryList.map((values) => ({
    value: values.id,
    label: values.title,
  }));
  /* **************** End Category List ******************* */

  /* **************** Start Fetch Subcategories Based On Category ******************* */
  const [selectedCategoryId, setSelectedCategoryId] = useState(
    productData?.category_id || null
  );
  const { data: subCategoriesData } = useGetAllSubCategoriesQuery(
    { category_id: selectedCategoryId },
    { skip: !selectedCategoryId }
  );
  const subCategoriesList = useMemo(() => {
    if (!subCategoriesData?.data?.length) {
      return [];
    }
    return subCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [subCategoriesData?.data]);
  /* **************** End Fetch Subcategories Based On Category ******************* */

  /* **************** Start Handle category selection change ******************* */
  const handleCategoryChange = (e) => {
    setSelectedCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle category selection change ******************* */

  /* **************** Start Fetch childCategories Based On SubCategory ******************* */
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(
    productData?.sub_category_id || null
  );
  const { data: childCategoriesData } = useGetAllChildCategoriesQuery(
    { sub_category_id: selectedSubCategoryId },
    { skip: !selectedSubCategoryId }
  );
  const childCategoriesList = useMemo(() => {
    if (!childCategoriesData?.data?.length) {
      return [];
    }
    return childCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [childCategoriesData?.data]);
  /* **************** End Fetch childCategories Based On SubCategory ******************* */

  /* **************** Start Handle sub category selection change ******************* */
  const handleSubCategoryChange = (e) => {
    setSelectedSubCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle sub category selection change ******************* */

  const { showSimpleConfirm } = useConfirmDelete({
    title: "Delete Product?",
    text: "Are you sure you want to delete this product?",
    confirmButtonText: "Yes, delete product!",
    cancelButtonText: "Cancel",
  });
  const [
    handleDeleteProductDetailApi,
    { isLoading: isDeleteProductDetailLoading },
  ] = useDeleteProductDetailMutation();
  const onDeleteProductDetailHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
    if (confirmed) {
      try {
        const body = { id: id };
        const resp = await handleDeleteProductDetailApi(body).unwrap();
        handleApiSuccess(resp);
        navigation("/products"); // Redirect to the desired page
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };

  const onEditProductDetailsHandler = (d) => {
    setEditingProductDetailData(d);
    setIsEditingProductDetail(true);
    // Set product attributes from variations
    setProductAttributes(singleProductVariations?.data || []);
  };

  /* **************** Start List Attributes List ******************* */
  const attributeResp = useListAllAttributesQuery();
  const attribute = useMemo(
    () => attributeResp.data?.data || [],
    [attributeResp.data?.data]
  );
  const attributesAry = useMemo(
    () =>
      attribute.map((attribute) => ({
        value: attribute.id.toString(),
        label: attribute.title,
      })),
    [attribute]
  );
  /* **************** End List Attributes List ******************* */

  // Updated EditProductValues with attribute_id
  const EditProductValues = useMemo(
    () =>
      !productData
        ? { ...initialValues, attribute_id: variationAttributeIds }
        : {
          id: productData?.id || "",
          branch_id: productData?.branch_id || "",
          title: productData?.title || "",
          title_ar: productData?.title_ar || "",
          brand_id: productData?.brand_id || "",
          category_id: productData?.category_id || "",
          sub_category_id: productData?.sub_category_id || "",
          child_category_id: productData?.child_category_id || "",
          description: productData?.description || "",
          description_ar: productData?.description_ar || "",
          is_publish: productData?.is_publish || "",
          note: productData?.note || "",
          product_slug: productData?.slug || "",
          attribute_id: variationAttributeIds, // Pre-select attribute IDs
        },
    [productData, variationAttributeIds]
  );

  // Create/Update Product
  const [handleUpdateProductBasicApi, { isLoading: isLoading }] =
    useEditProductMutation();
  const handleSubmit = async (body) => {
    try {
      const formData = new FormData();
      for (const [key, value] of Object.entries(body)) {
        if (key === "image" && value) {
          formData.append(key, value);
        } else if (
          key === "is_publish" ||
          key === "category_id" ||
          (key === "sub_category_id" && value !== "") ||
          (key === "child_category_id" && value !== "") ||
          key === "brand_id"
        ) {
          formData.append(key, parseInt(value));
        } else if (key === "attribute_id") {
          // Handle attribute_id as an array
          value.forEach((id) => formData.append("attribute_id[]", id));
        } else {
          formData.append(key, value);
        }
      }
      const resp = await handleUpdateProductBasicApi(formData).unwrap();
      handleApiSuccess(resp);
      handleAssignVariationSubmitFunction({
        product_id: resp.data,
        attribute_id: body.attribute_id,
      });
      // setDetailsTabDisplay("block");
      setActiveTab("product-details");
      // setSelectedBranchId(body.branch_id);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  const [
    handleAssignVariationDataApi,
    { isLoading: isAssignVariationLoading },
  ] = useCreateUpdateVariationsProductsMutation();
  const handleAssignVariationSubmitFunction = async (body) => {
    try {
      const attributeIds = Array.isArray(body.attribute_id)
        ? body.attribute_id.map((id) => parseInt(id))
        : [];
      const updatedBody = {
        ...body,
        product_id: parseInt(body.product_id),
        attribute_id: attributeIds,
      };
      await handleAssignVariationDataApi(updatedBody).unwrap();
      // setSelectedProductId(body.product_id);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Tab Management ******************* */
  const [activeTab, setActiveTab] = useState("product-basics");
  // const [detailsTabDisplay, setDetailsTabDisplay] = useState("block");

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };
  /* **************** End Tab Management ******************* */

  /* **************** Product Details Edit State ******************* */
  const [isEditingProductDetail, setIsEditingProductDetail] = useState(false);
  const [isCreatingProductDetail, setIsCreatingProductDetail] = useState(false);
  console.log(isCreatingProductDetail);

  const [editingProductDetailData, setEditingProductDetailData] =
    useState(null);
  const currentFormValuesRef = useRef({});
  /* **************** End Product Details Edit State ******************* */

  /* **************** Product Attributes State ******************* */
  const [productAttributes, setProductAttributes] = useState([]);

  // Create attributes map for titles
  const attributesMap = useMemo(() => {
    const map = {};
    attribute.forEach((attr) => {
      map[attr.id] = attr.title;
    });
    return map;
  }, [attribute]);
  /* **************** End Product Attributes State ******************* */

  /* **************** Product Detail Functions ******************* */
  const getProductDetailValidationSchema = (productAttributes) => {
    const baseValidation = {
      bar_code: yup.string().required().label("Barcode"),
      sku: yup.string().required().label("Sku"),
      description: yup.string().label("Description"),
      description_ar: yup.string().label("Description Arabic"),
      is_publish: yup.string().required().label("Status"),
    };

    // Add validation for each attribute
    productAttributes.forEach((attr) => {
      const fieldName = `attribute_${attr.attribute_id}`;
      baseValidation[fieldName] = yup
        .string()
        .required()
        .label(
          attributesMap[attr.attribute_id] || `Attribute ${attr.attribute_id}`
        );
    });

    return yup.object().shape(baseValidation);
  };

  const getProductDetailInitialValues = (
    productAttributes,
    currentValues = {},
    editData = null
  ) => {
    const baseValues = {
      product_id: currentValues.product_id || productId || "",
      bar_code: currentValues.bar_code || editData?.bar_code || "",
      description: currentValues.description || editData?.description || "",
      description_ar:
        currentValues.description_ar || editData?.description_ar || "",
      sku: currentValues.sku || editData?.sku || "",
      is_publish: currentValues.is_publish || editData?.is_publish || "",
    };

    // Parse the attributes array from edit data if available
    let attributesArray = [];
    if (editData?.attributes) {
      try {
        attributesArray = JSON.parse(editData.attributes);
      } catch (e) {
        console.error("Error parsing attributes:", e);
        attributesArray = [];
      }
    }

    // Add attribute fields, preserving existing values or setting from edit data
    productAttributes.forEach((attr) => {
      const fieldName = `attribute_${attr.attribute_id}`;
      baseValues[fieldName] = currentValues[fieldName] || "";
      if (attributesArray.length > 0) {
        const matchingAttributeValue = attributesArray.find((attrValueId) => {
          return attrValueId;
        });

        if (matchingAttributeValue) {
          baseValues[fieldName] = matchingAttributeValue;
        }
      }
    });

    return baseValues;
  };

  const AttributeValueSelect = ({
    attributeId,
    attributeTitle,
    attributesArray = [],
    formikProps,
  }) => {
    const {
      data: attributeValuesData,
      isLoading,
      error,
    } = useGetAllAttributesValuesQuery(
      { attribute_id: attributeId },
      { skip: !attributeId }
    );

    const attributeValueOptions = useMemo(() => {
      if (!attributeValuesData?.data?.length) {
        return [];
      }
      return attributeValuesData.data.map((value) => ({
        value: value.id,
        label: value.title,
      }));
    }, [attributeValuesData?.data]);

    // Set the pre-selected value when attribute values are loaded
    useEffect(() => {
      if (
        attributeValuesData?.data?.length &&
        attributesArray.length &&
        formikProps
      ) {
        const fieldName = `attribute_${attributeId}`;
        const currentValue = formikProps.values[fieldName];

        // Only set if field is currently empty
        if (!currentValue || currentValue === "") {
          const preSelectedValue = attributeValuesData.data.find((value) =>
            attributesArray.includes(value.id)
          );

          if (preSelectedValue) {
            formikProps.setFieldValue(
              fieldName,
              preSelectedValue.id.toString()
            );
          }
        }
      }
    }, [attributeValuesData?.data, attributesArray, attributeId, formikProps]);

    return (
      attributeValueOptions.length > 0 &&
      !isLoading &&
      !error && (
        <div className="col-lg-6">
          <div className="mb-3">
            <label htmlFor={`attribute_${attributeId}`} className="form-label">
              {attributeTitle}
              <span className="un-validation">(*)</span>
              {isLoading && <small className="text-muted"> (Loading...)</small>}
            </label>
            <FormikField
              name={`attribute_${attributeId}`}
              id={`attribute_${attributeId}`}
              className="form-select"
              type="select"
              options={attributeValueOptions}
            />
            {error && (
              <small className="text-danger">
                Error loading attribute values
              </small>
            )}
          </div>
        </div>
      )
    );
  };

  const [
    handleEditProductDetailApi,
    { isLoading: isEditProductDetailLoading },
  ] = useEditProductDetailMutation();
  const handleProductDetailsSubmit = async (body) => {
    try {
      const attributes = [];
      Object.keys(body).forEach((key) => {
        if (key.startsWith("attribute_") && body[key] && body[key] !== "") {
          attributes.push(parseInt(body[key]));
        }
      });

      const productDetail = {
        id: editingProductDetailData?.id, // Include ID for editing
        branch_id: parseInt(1),
        product_id: parseInt(productId),
        attributes: attributes.length > 0 ? JSON.stringify(attributes) : null,
        sku: body.sku,
        bar_code: body.bar_code,
        description: body.description || "",
        description_ar: body.description_ar || "",
        is_publish: parseInt(body.is_publish),
      };

      const resp = await handleEditProductDetailApi(productDetail).unwrap();
      handleApiSuccess(resp);

      // Reset edit state and show table
      setIsEditingProductDetail(false);
      setIsCreatingProductDetail(false);
      setEditingProductDetailData(null);
      currentFormValuesRef.current = {};
    } catch (error) {
      handleApiErrors(error);
    }
  };

  const handleCreateProductDetail = () => {
    setIsCreatingProductDetail(true);
    setIsEditingProductDetail(false);
  };

  const [createProductAttributes, setCreateProductAttributes] = useState([]);
  const [isCreateVariationLoading, setIsCreateVariationLoading] =
    useState(false);

  /* **************** Start Fetch Product Variations for Create ******************* */
  useEffect(() => {
    if (productId) {
      const fetchCreateProductVariations = async () => {
        setIsCreateVariationLoading(true);
        try {
          const response = await getVariationsProducts({
            product_id: parseInt(productId),
          }).unwrap();
          if (
            response?.data &&
            Array.isArray(response.data) &&
            response.data.length > 0
          ) {
            setCreateProductAttributes(response.data);
          } else {
            setCreateProductAttributes([]);
          }
        } catch (error) {
          console.error("Error fetching product variations for create:", error);
          setCreateProductAttributes([]);
        } finally {
          setIsCreateVariationLoading(false);
        }
      };
      fetchCreateProductVariations();
    } else {
      setCreateProductAttributes([]);
    }
  }, [productId, getVariationsProducts]);

  /* **************** Create Attribute Value Select Component ******************* */
  const CreateAttributeValueSelect = ({
    attributeId,
    attributeTitle,
    formikProps,
  }) => {
    const { data: attributeValuesData, isLoading, error } = useGetAllAttributesValuesQuery(
      { attribute_id: attributeId },
      { skip: !attributeId }
    );
  
    const attributeValueOptions = useMemo(() => {
      if (!attributeValuesData?.data?.length) {
        return [{ value: "", label: "Select an option" }]
      }
      return [
      { value: "", label: "Select an option" }, // Include empty option as default
      ...attributeValuesData.data.map((value) => ({
        value: value.id,
        label: value.title,
      })),
    ];
    }, [attributeValuesData?.data]);
  
    return (
      attributeValueOptions.length > 0 && !isLoading && !error && (
        <div className="col-lg-6">
          <div className="mb-3">
            <label htmlFor={`attribute_${attributeId}`} className="form-label">
              {attributeTitle}              
              {isLoading && <small className="text-muted"> (Loading...)</small>}
            </label>
            <FormikField
              name={`attribute_${attributeId}`}
              id={`attribute_${attributeId}`}
              className="form-select"
              type="select"
              options={attributeValueOptions}
            />
            {error && <small className="text-danger">Error loading attribute values</small>}
          </div>
        </div>
      )
    );
  };

  const [handleCreateProductDetailsApi, {isLoading: isCreateProductDetailsLoading}] = useCreateProductDetailMutation();
      const handleProductDetailsCreateSubmit = async (body) => {
        try {
          const attributes = [];
          Object.keys(body).forEach(key => {
            if (key.startsWith('attribute_') && body[key] && body[key] !== "") {
              attributes.push(parseInt(body[key]));
            }
          });
    
          const productDetail = {
            branch_id: 1, //. for now sending 1 will update later 
            product_id: parseInt(productId),
            attributes: attributes.length > 0 ? JSON.stringify(attributes) : null,
            sku: body.sku,
            bar_code: body.bar_code,
            description: body.description || "",
            description_ar: body.description_ar || "",
            is_publish: parseInt(body.is_publish)
          };
          const resp = await handleCreateProductDetailsApi(productDetail).unwrap();
          handleApiSuccess(resp);
          setIsCreatingProductDetail(false)
          // navigate(`/editProduct/${resp.data}`);
        } catch (error) {
          handleApiErrors(error);
        }
      };

  /* **************** End Product Detail Functions ******************* */

  /* **************** Web Loader  ******************* */
  if (
    isLoading ||
    isProductLoading ||
    isAssignVariationLoading ||
    isProductVariationLoading ||
    attributeResp.isLoading ||
    isProductDetailListingLoading ||
    isDeleteProductDetailLoading ||
    isEditProductDetailLoading ||
    isCreateVariationLoading || 
    isCreateProductDetailsLoading
  )
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <ul
                      className="nav nav-pills user-profile-tab"
                      id="pills-tab"
                      role="tablist"
                    >
                      <li className="nav-item" role="presentation">
                        <button
                          className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${activeTab === "product-basics" ? "active" : ""
                            }`}
                          type="button"
                          onClick={() => handleTabClick("product-basics")}
                        >
                          <i className="ti ti-list me-2 fs-6"></i>
                          <span className="d-none d-md-block">
                            Product Basics
                          </span>
                        </button>
                      </li>
                      <li className="nav-item" role="presentation">
                        <button
                          className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${activeTab === "product-details" ? "active" : ""
                            }`}
                          type="button"
                          onClick={() => handleTabClick("product-details")}
                        >
                          <i className="ti ti-file-invoice me-2 fs-6"></i>
                          <span className="d-none d-md-block">
                            Product Details
                          </span>
                        </button>
                      </li>
                    </ul>
                    <div className="card-body">
                      <div className="tab-content" id="pills-tabContent">
                        {/* Start Product Basics Tab */}
                        {activeTab === "product-basics" && (
                          <div
                            className="tab-pane fade show active"
                            id="pills-account"
                            role="tabpanel"
                            aria-labelledby="pills-account-tab"
                            tabIndex="0"
                          >
                            <div className="row">
                              <div className="col-lg-12 d-flex align-items-stretch">
                                <div className="card w-100 border position-relative overflow-hidden mb-0">
                                  <div className="card-body p-4">
                                    <h4 className="card-title">
                                      Product Basics
                                    </h4>
                                    <p className="card-subtitle mb-4">
                                      To create Product Basics, add details and
                                      save from here
                                    </p>
                                    <Formik
                                      initialValues={EditProductValues}
                                      validationSchema={validation}
                                      onSubmit={handleSubmit}
                                      enableReinitialize={true} // Ensure form reinitializes when EditProductValues changes
                                    >
                                      <Form
                                        name="product-create"
                                        className="needs-validation"
                                        autoComplete="on"
                                        encType="multipart/form-data"
                                      >
                                        <div className="row">
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="branch_id"
                                                className="form-label"
                                              >
                                                Branch
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="branch_id"
                                                id="branch_id"
                                                className="form-select"
                                                type="select"
                                                options={branchesList}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6"></div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="category_id"
                                                className="form-label"
                                              >
                                                Category
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="category_id"
                                                id="category_id"
                                                className="form-select"
                                                type="select"
                                                options={categoriesList}
                                                onChange={handleCategoryChange}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="sub_category_id"
                                                className="form-label"
                                              >
                                                Sub Category
                                              </label>
                                              <FormikField
                                                name="sub_category_id"
                                                id="sub_category_id"
                                                className="form-select"
                                                type="select"
                                                options={subCategoriesList}
                                                onChange={
                                                  handleSubCategoryChange
                                                }
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="child_category_id"
                                                className="form-label"
                                              >
                                                Child Category
                                              </label>
                                              <FormikField
                                                name="child_category_id"
                                                id="child_category_id"
                                                className="form-select"
                                                type="select"
                                                options={childCategoriesList}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="brand_id"
                                                className="form-label"
                                              >
                                                Brands
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="brand_id"
                                                id="brand_id"
                                                className="form-select"
                                                type="select"
                                                options={brandAry}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="title"
                                                className="form-label"
                                              >
                                                Product Title in English
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="text"
                                                name="title"
                                                id="title"
                                                placeholder="Product Title in English *"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="title_ar"
                                                className="form-label"
                                              >
                                                Product Title in Arabic
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="text"
                                                name="title_ar"
                                                id="title_ar"
                                                placeholder="Product Title in Arabic *"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description"
                                                className="form-label"
                                              >
                                                Product Description in English
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description"
                                                id="description"
                                                placeholder="Product Description in English"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description_ar"
                                                className="form-label"
                                              >
                                                Product Description in Arabic
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description_ar"
                                                id="description_ar"
                                                placeholder="Product Description in Arabic"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="note"
                                                className="form-label"
                                              >
                                                Note
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="note"
                                                id="note"
                                                placeholder="Note"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="image"
                                                className="form-label"
                                              >
                                                Product Image
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="file"
                                                name="image"
                                                id="image"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="is_publish"
                                                className="form-label"
                                              >
                                                Product Status
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="is_publish"
                                                id="is_publish"
                                                className="form-select"
                                                type="select"
                                                options={generalStatusList}
                                              />
                                            </div>
                                          </div>
                                          <hr />
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="attribute_id"
                                                className="form-label"
                                              >
                                                Assign Product Variations
                                                <span className="ms-1">
                                                  (Optional)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="attribute_id"
                                                id="attribute_id"
                                                type="checkbox-group"
                                                options={attributesAry}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-12">
                                            <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                              <button
                                                className="btn btn-primary"
                                                type="submit"
                                              >
                                                Update Product
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      </Form>
                                    </Formik>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        {/* End Product Basics Tab */}
                        {/* Start Product Details Listing Tab */}
                        {activeTab === "product-details" && (
                          <>
                            {/* Show Edit Form when editing */}
                            {isEditingProductDetail &&
                              !isCreatingProductDetail && (
                                <div className="row">
                                  <div className="col-lg-12 d-flex align-items-stretch">
                                    <div className="card w-100 border position-relative overflow-hidden mb-0">
                                      <div className="card-body p-4">
                                        <div className="d-flex justify-content-between align-items-center mb-4">
                                          <div>
                                            <h4 className="card-title">
                                              Edit Product Details
                                            </h4>
                                            <p className="card-subtitle mb-0">
                                              Update product details and save
                                              changes
                                            </p>
                                          </div>
                                          <button
                                            type="button"
                                            className="btn btn-secondary"
                                            onClick={() => {
                                              setIsEditingProductDetail(false);
                                              setEditingProductDetailData(null);
                                              currentFormValuesRef.current = {};
                                            }}
                                          >
                                            Cancel
                                          </button>
                                        </div>
                                        <Formik
                                          initialValues={getProductDetailInitialValues(
                                            productAttributes,
                                            currentFormValuesRef.current,
                                            editingProductDetailData
                                          )}
                                          enableReinitialize={true}
                                          validationSchema={getProductDetailValidationSchema(
                                            productAttributes,
                                            attributesMap
                                          )}
                                          onSubmit={handleProductDetailsSubmit}
                                        >
                                          {(formikProps) => {
                                            currentFormValuesRef.current =
                                              formikProps.values;
                                            return (
                                              <Form
                                                name="product-edit"
                                                className="needs-validation"
                                                autoComplete="off"
                                              >
                                                <div className="row">
                                                  <>
                                                    <hr></hr>
                                                    <h4 className="card-title">
                                                      Product Details
                                                    </h4>
                                                    {/* Start Dynamic Attribute Value Select Fields */}
                                                    {productAttributes.length >
                                                      0 && (
                                                        <>
                                                          {productAttributes.map(
                                                            (attr) => (
                                                              <AttributeValueSelect
                                                                key={
                                                                  attr.attribute_id
                                                                }
                                                                attributeId={
                                                                  attr.attribute_id
                                                                }
                                                                attributeTitle={
                                                                  attributesMap[
                                                                  attr
                                                                    .attribute_id
                                                                  ] ||
                                                                  `Attribute ${attr.attribute_id}`
                                                                }
                                                                attributesArray={
                                                                  editingProductDetailData?.attributes_array ||
                                                                  []
                                                                }
                                                                formikProps={
                                                                  formikProps
                                                                }
                                                              />
                                                            )
                                                          )}
                                                        </>
                                                      )}
                                                    {/* End Dynamic Attribute Value Select Fields */}
                                                    <div className="col-lg-6">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="bar_code"
                                                          className="form-label"
                                                        >
                                                          Barcode
                                                          <span className="un-validation">
                                                            (*)
                                                          </span>
                                                        </label>
                                                        <FormikField
                                                          type="text"
                                                          name="bar_code"
                                                          id="bar_code"
                                                          placeholder="Enter barcode *"
                                                          className="form-control"
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-6">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="sku"
                                                          className="form-label"
                                                        >
                                                          Sku
                                                          <span className="un-validation">
                                                            (*)
                                                          </span>
                                                        </label>
                                                        <FormikField
                                                          type="text"
                                                          name="sku"
                                                          id="sku"
                                                          placeholder="Enter sku *"
                                                          className="form-control"
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-6">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="description"
                                                          className="form-label"
                                                        >
                                                          Description
                                                        </label>
                                                        <FormikField
                                                          type="textarea"
                                                          name="description"
                                                          id="description"
                                                          placeholder="Enter description"
                                                          className="form-control"
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-6">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="description_ar"
                                                          className="form-label"
                                                        >
                                                          Description Arabic
                                                        </label>
                                                        <FormikField
                                                          type="textarea"
                                                          name="description_ar"
                                                          id="description_ar"
                                                          placeholder="Enter description arabic"
                                                          className="form-control"
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-6">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="is_publish"
                                                          className="form-label"
                                                        >
                                                          Status
                                                          <span className="un-validation">
                                                            (*)
                                                          </span>
                                                        </label>
                                                        <FormikField
                                                          name="is_publish"
                                                          id="is_publish"
                                                          className="form-select"
                                                          type="select"
                                                          options={
                                                            generalStatusList
                                                          }
                                                        />
                                                      </div>
                                                    </div>
                                                  </>

                                                  <div className="col-12">
                                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                                      <button
                                                        className="btn btn-primary"
                                                        type="submit"
                                                      >
                                                        Update Product Detail
                                                      </button>
                                                    </div>
                                                  </div>
                                                </div>
                                              </Form>
                                            );
                                          }}
                                        </Formik>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}

                            {/* Start Show Create Form */}
                            {isCreatingProductDetail && (
                              <div className="row">
                                <div className="col-lg-12 d-flex align-items-stretch">
                                  <div className="card w-100 border position-relative overflow-hidden mb-0">
                                    <div className="card-body p-4">
                                      <div className="d-flex justify-content-between align-items-center mb-4">
                                          <div>
                                            <h4 className="card-title">
                                              Create Product Details
                                            </h4>
                                            <p className="card-subtitle mb-0">
                                              Create product details and save
                                              changes
                                            </p>
                                          </div>
                                          <button
                                            type="button"
                                            className="btn btn-secondary"
                                            onClick={() => {
                                              setIsCreatingProductDetail(false);
                                              setCreateProductAttributes([]);
                                              currentFormValuesRef.current = {};
                                            }}
                                          >
                                            Cancel
                                          </button>
                                        </div>
                                      <Formik
                                        initialValues={getProductDetailInitialValues(
                                          productAttributes,
                                          currentFormValuesRef.current
                                        )}
                                        enableReinitialize={true}
                                        validationSchema={getProductDetailValidationSchema(
                                          productAttributes,
                                          attributesMap
                                        )}
                                        onSubmit={handleProductDetailsCreateSubmit}
                                      >
                                        {(formikProps) => {
                                          currentFormValuesRef.current =
                                            formikProps.values;
                                          return (
                                            <Form
                                              name="product-create"
                                              className="needs-validation"
                                              autoComplete="off"
                                            >
                                              <div className="row">
                                                <>
                                                  <hr></hr>
                                                  <h4 className="card-title">
                                                    Product Details
                                                  </h4>
                                                  {/* Start Dynamic Attribute Value Select Fields */}
                                                  {createProductAttributes.length >
                                                    0 && (
                                                      <>
                                                        {createProductAttributes.map(
                                                          (attr) => (
                                                            <CreateAttributeValueSelect
                                                              key={
                                                                attr.attribute_id
                                                              }
                                                              attributeId={
                                                                attr.attribute_id
                                                              }
                                                              attributeTitle={
                                                                attributesMap[
                                                                attr
                                                                  .attribute_id
                                                                ] ||
                                                                `Attribute ${attr.attribute_id}`
                                                              }
                                                              formikProps={
                                                                formikProps
                                                              }
                                                            />
                                                          )
                                                        )}
                                                      </>
                                                    )}
                                                  {/* End Dynamic Attribute Value Select Fields */}
                                                  <div className="col-lg-6">
                                                    <div className="mb-3">
                                                      <label
                                                        htmlFor="bar_code"
                                                        className="form-label"
                                                      >
                                                        Barcode
                                                        <span className="un-validation">
                                                          (*)
                                                        </span>
                                                      </label>
                                                      <FormikField
                                                        type="text"
                                                        name="bar_code"
                                                        id="bar_code"
                                                        placeholder="Enter barcode *"
                                                        className="form-control"
                                                      />
                                                    </div>
                                                  </div>
                                                  <div className="col-lg-6">
                                                    <div className="mb-3">
                                                      <label
                                                        htmlFor="sku"
                                                        className="form-label"
                                                      >
                                                        Sku
                                                        <span className="un-validation">
                                                          (*)
                                                        </span>
                                                      </label>
                                                      <FormikField
                                                        type="text"
                                                        name="sku"
                                                        id="sku"
                                                        placeholder="Enter sku *"
                                                        className="form-control"
                                                      />
                                                    </div>
                                                  </div>
                                                  <div className="col-lg-6">
                                                    <div className="mb-3">
                                                      <label
                                                        htmlFor="description"
                                                        className="form-label"
                                                      >
                                                        Description
                                                      </label>
                                                      <FormikField
                                                        type="textarea"
                                                        name="description"
                                                        id="description"
                                                        placeholder="Enter description"
                                                        className="form-control"
                                                      />
                                                    </div>
                                                  </div>
                                                  <div className="col-lg-6">
                                                    <div className="mb-3">
                                                      <label
                                                        htmlFor="description_ar"
                                                        className="form-label"
                                                      >
                                                        Description Arabic
                                                      </label>
                                                      <FormikField
                                                        type="textarea"
                                                        name="description_ar"
                                                        id="description_ar"
                                                        placeholder="Enter description arabic"
                                                        className="form-control"
                                                      />
                                                    </div>
                                                  </div>
                                                  <div className="col-lg-6">
                                                    <div className="mb-3">
                                                      <label
                                                        htmlFor="is_publish"
                                                        className="form-label"
                                                      >
                                                        Status
                                                        <span className="un-validation">
                                                          (*)
                                                        </span>
                                                      </label>
                                                      <FormikField
                                                        name="is_publish"
                                                        id="is_publish"
                                                        className="form-select"
                                                        type="select"
                                                        options={
                                                          generalStatusList
                                                        }
                                                      />
                                                    </div>
                                                  </div>
                                                </>

                                                <div className="col-12">
                                                  <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                                    <button
                                                      className="btn btn-primary"
                                                      type="submit"
                                                    >
                                                      Add Product Detail
                                                    </button>
                                                  </div>
                                                </div>
                                              </div>
                                            </Form>
                                          );
                                        }}
                                      </Formik>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                            {/* End Show Create Form */}

                            {/* Show Table when not editing */}
                            {!isEditingProductDetail &&
                              !isCreatingProductDetail && (
                                <div
                                  className="tab-pane fade show active"
                                  id="pills-account"
                                  role="tabpanel"
                                  aria-labelledby="pills-account-tab"
                                  tabIndex="0"
                                >
                                  <div className="row">
                                    <div className="col-lg-12 d-flex align-items-stretch">
                                      <div className="card w-100 border position-relative overflow-hidden mb-0">
                                        <div className="px-4 py-3 border-bottom">
                                          <div className="d-sm-flex align-items-center justify-space-between">
                                            <h4 className="card-title mb-0">
                                              Product Details List
                                            </h4>
                                            <nav
                                              aria-label="breadcrumb"
                                              className="ms-auto"
                                            >
                                              <ol className="breadcrumb">
                                                <li
                                                  className="breadcrumb-item"
                                                  aria-current="page"
                                                >
                                                  <button
                                                    type="button"
                                                    className="btn btn-primary"
                                                    onClick={() => {
                                                      handleCreateProductDetail();
                                                    }}
                                                  >
                                                    Create Product Detail
                                                  </button>
                                                </li>
                                              </ol>
                                            </nav>
                                          </div>
                                        </div>
                                        <div className="card-body">
                                          <div className="table-responsive">
                                            <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                              <div className="d-flex gap-6">
                                                <div>
                                                  <select
                                                    value={filterStatus}
                                                    className="form-control search-chat py-2 "
                                                    onChange={
                                                      handleStatusFilter
                                                    }
                                                  >
                                                    <option value="">
                                                      All Status
                                                    </option>
                                                    {generalStatusList.map(
                                                      (option) => (
                                                        <option
                                                          key={option.value}
                                                          value={option.value}
                                                        >
                                                          {option.label}
                                                        </option>
                                                      )
                                                    )}
                                                  </select>
                                                </div>
                                              </div>
                                              <div className="position-relative">
                                                <input
                                                  type="text"
                                                  className="form-control search-chat py-2 ps-5"
                                                  id="text-srh"
                                                  onChange={
                                                    handleKeywordsFilter
                                                  }
                                                  placeholder="Keyword Search..."
                                                  value={filterKeywords}
                                                />
                                                <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                              </div>
                                            </div>
                                            <Table
                                              headCells={[
                                                {
                                                  key: "sel_id",
                                                  label: "#",
                                                  align: "left",
                                                },
                                                {
                                                  key: "bar_code",
                                                  label: "Bar Code",
                                                  align: "left",
                                                },
                                                {
                                                  key: "sku",
                                                  label: "SKU",
                                                  align: "left",
                                                },
                                                {
                                                  key: "status_name",
                                                  key_id: "status",
                                                  label: "Status",
                                                  align: "left",
                                                },
                                              ]}
                                              data={productDetailList}
                                              onDeleteHandler={
                                                onDeleteProductDetailHandler
                                              }
                                              onEditHandler={
                                                onEditProductDetailsHandler
                                              }
                                            />
                                            <PaginationComponent
                                              totalCount={pageData?.total_count}
                                              pageSize={pageData?.page_size}
                                              currentPage={currentPage}
                                              setCurrentPage={setCurrentPage}
                                              onPageChange={fetchData}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}
                          </>
                        )}
                        {/* End Product Details Tab */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
